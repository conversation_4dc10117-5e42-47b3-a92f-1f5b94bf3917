package se.visionmate.vcommerce.api

import grails.gorm.transactions.Transactional
import groovyx.net.http.HttpException
import org.apache.commons.lang.RandomStringUtils
import org.springframework.beans.factory.annotation.Value
import se.visionmate.vcommerce.core.Address
import se.visionmate.vcommerce.core.AppService
import se.visionmate.vcommerce.core.Attachment
import se.visionmate.vcommerce.core.Attribute
import se.visionmate.vcommerce.core.AttributeTerm
import se.visionmate.vcommerce.core.Category
import se.visionmate.vcommerce.core.CategoryText
import se.visionmate.vcommerce.core.Contact
import se.visionmate.vcommerce.core.Customer
import se.visionmate.vcommerce.core.Discount
import se.visionmate.vcommerce.core.ErrorHandlerService
import se.visionmate.vcommerce.core.Location
import se.visionmate.vcommerce.core.Option
import se.visionmate.vcommerce.core.PacketService
import se.visionmate.vcommerce.core.Price
import se.visionmate.vcommerce.core.PriceClass
import se.visionmate.vcommerce.core.PriceService
import se.visionmate.vcommerce.core.Product
import se.visionmate.vcommerce.core.ProductService
import se.visionmate.vcommerce.core.Tag
import se.visionmate.vcommerce.core.TagService
import se.visionmate.vcommerce.enums.AddressType
import se.visionmate.vcommerce.enums.ApiService
import se.visionmate.vcommerce.enums.BatchUpdateType
import se.visionmate.vcommerce.enums.ContactType
import se.visionmate.vcommerce.enums.Currency
import se.visionmate.vcommerce.enums.CustomerType
import se.visionmate.vcommerce.enums.FileExtension
import se.visionmate.vcommerce.enums.HttpMethod
import se.visionmate.vcommerce.enums.Language
import se.visionmate.vcommerce.enums.OptionType
import se.visionmate.vcommerce.enums.PacketOption
import se.visionmate.vcommerce.enums.PriceClassType
import se.visionmate.vcommerce.enums.PriceType
import se.visionmate.vcommerce.enums.ProductStatus
import se.visionmate.vcommerce.enums.ProductType
import se.visionmate.vcommerce.utils.OAuth1AuthorizationUtil

@Transactional
class WooConnectService {
    private static String apiUri
    private static String apiPath
    private static String apiVersion
    private static String consumerKey
    private static String consumerSecret
    private static String attachmentPath
    private static String webArticleAttributeVismaId
    private static String shortDescriptionAttributeVismaId
    private static String hidePubliclyAttributeVismaId
    private static String licenceSoftwareAttributeVismaId
    private static String newProductAttributeVismaId
    private static String imageSortingField
    private static String defaultCategoryId
    private static String pdfIconUrn
    private static String docIconUrn

    private static final String webArticleAttributeMetaDataKey = '_wvs_is_visible'
    private static final String hidePubliclyAttributeMetaDataKey = '_wvs_is_hidden_publicly'
    private static final String licenceSoftwareAttributeMetaDataKey = '_wvs_is_licence_software'
    private static final String newProductAttributeMetaDataKey = '_wvs_new_product_expiration_date'

    AppService appService
    PacketService packetService
    HttpService httpService
    TagService tagService
    ProductService productService
    PriceService priceService
    ErrorHandlerService errorHandlerService

    @Value('${appConfig.productCategory.wooApiProperty.display}')
    String productCategoryWooApiPropDisplay

    /**
     * Initialization with Woo configuration data
     */
    void initialize() {
        List<Option> options = Option.findAllByType(OptionType.WOO_SETTING)
        if (!options) {
            println "[$appService.timeStamp] No options for Woo settings"
            System.exit(0)
        }
        options.each { Option option ->
            switch (option.code) {
                case 'woo_api_uri':
                    apiUri = option.value
                    break
                case 'woo_api_path':
                    apiPath = option.value
                    break
                case 'woo_api_version':
                    apiVersion = option.value
                    break
                case 'woo_consumer_key':
                    consumerKey = option.value
                    break
                case 'woo_consumer_secret':
                    consumerSecret = option.value
                    break
                case 'woo_attachment_path':
                    attachmentPath = option.value
                    break
                case 'web_article_attribute_visma_id':
                    webArticleAttributeVismaId = option.value
                    break
                case 'short_description_attribute_visma_id':
                    shortDescriptionAttributeVismaId = option.value
                    break
                case 'hide_publicly_attribute_visma_id':
                    hidePubliclyAttributeVismaId = option.value
                    break
                case 'licence_software_attribute_visma_id':
                    licenceSoftwareAttributeVismaId = option.value
                    break
                case 'new_product_attribute_visma_id':
                    newProductAttributeVismaId = option.value
                    break
                case 'image_sorting_field':
                    imageSortingField = option.value
                    break
                case 'default_category_id':
                    defaultCategoryId = option.value
                    break
                case 'pdf_icon_urn':
                    pdfIconUrn = option.value
                    break
                case 'doc_icon_urn':
                    docIconUrn = option.value
                    break
                default:
                    println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] ${option.name} not presented"
                    System.exit(0)
            }
        }
    }

    /**
     * Getting MAP with config fields for sending request to Visma
     * @return requestConfig as Map
     */
    Map<String, String> getRequestConfig(HttpMethod httpMethod, String target, Map extraParams = [:]) {
        String auth = apiUri.startsWith('https') ?
                "Basic " + "${consumerKey}:${consumerSecret}".bytes.encodeBase64().toString() :
                OAuth1AuthorizationUtil.generateOAuthAuthorizationHeader(httpMethod.name(), getWooApiUrl(target), consumerKey, consumerSecret, extraParams)
        if (appService.testMode) {
            return [
                    'requestUri' : '',
                    'apiPath' : '/wp-json/wc/',
                    'apiVersion' : 'v3',
                    'auth' : auth
            ]
        } else {
            return [
                    'requestUri' : apiUri,
                    'apiPath' : apiPath,
                    'apiVersion' : apiVersion,
                    'auth' : auth
            ]
        }
    }

    /**
     * Get woo API url
     * @param target as String
     * @return
     */
    String getWooApiUrl(String target) {
        apiUri + apiPath + apiVersion + '/' + target
    }

    /**
     * Getting a country code suitable fow WooCommerce API
     * @param country as String
     * @return wooCountryCode as String
     */
    String getWooCountryCode(String country) {
        switch(country?.toLowerCase()) {
            case 'norge':
                return 'NO'
                break
            case 'finland':
                return 'FI'
                break
            case 'sverige':
                return 'SE'
                break
            default:
                return ''
        }

    }

    /**
     * Mapping a category to be suitable for import via the WooCommerce API with WPML support
     * @param category: Category
     * @param parentWooId: Integer
     * @param lang: Language (optional) - if provided, will map the category for this specific language
     * @return data: Map(suitable for WooCommerce API)
     */
    Map<String, ?> mapCategoryToWooApi(Category category, Integer parentWooId = null, Language lang = null) {
        Map<String, ?> data = [:]

        // If a specific language is provided and it's not the default language
        if (lang) {
            CategoryText categoryText = category.texts.find { it.lang == lang }

            data.name = categoryText?.name ?: category.name

            if (categoryText?.description) {
                data.description = categoryText.description
            } else if (category.description) {
                data.description = category.description
            }

            data.language = lang.code

            if (category.wooId) {
                data.translation_of = category.wooId
            }

            if (categoryText?.wooId) {
                data.id = categoryText.wooId
            }
        } else {
            // Default language behavior
            data.name = category.name

            if (category.description) {
                data.description = category.description
            }

            if (category.wooId) {
                data.id = category.wooId
            }
        }

        // Common properties for all languages
        data.display = productCategoryWooApiPropDisplay ?: 'default'

        if (parentWooId) {
            data.parent = parentWooId
        } else if (category.parentCategory?.wooId) {
            data.parent = category.parentCategory.wooId
        }

        if (category.slug) {
            data.slug = category.slug
        }

        if (category.sortOrder) {
            data.menu_order = category.sortOrder
        }

        return data
    }

    /**
     * Mapping an attribute/attributeTerm to be suitable for import via the WooCommerce API
     * @param object: Object(Entity) Attribute/AttributeTerm
     * @param toUpdate: Boolean (default = false)
     * @return data: Map
     */
    Map mapAttributeToWooApi(Object entity, Boolean toUpdate = false) {
        Map data = [:]

        if (entity instanceof Attribute) {

            if (toUpdate) {
                if (!entity.wooId) { return data }
                data.id = entity.wooId
            }

//            Attribute attribute = entity
            data.name = entity.name
            if (entity.wooId) { data.id = entity.wooId }
            if (entity.slug) { data.slug = entity.slug }
        } else if (entity instanceof AttributeTerm) {
//            AttributeTerm attributeTerm = entity
            data.name = entity.name
            if (entity.wooId) { data.id = entity.wooId }
            if (entity.slug) { data.slug = entity.slug }
            if (entity.description) { data.description = entity.description }
        }
        return data
    }

    /**
     * Mapping a customer to be suitable for import via the WooCommerce API
     * @param customer as Customer
     * @param toUpdate as Boolean(optional)
     * @return data as Map(suitable for WooCommerce API)
     */
    Map mapCustomerToWooApi(Customer customer, Boolean toUpdate = false) {
        customer.refresh()
        Map data = [:]

        if (toUpdate) {
            if (!customer.wooId) { return data }
            data.id = customer.wooId
        } else {
            data.password = RandomStringUtils.randomAlphanumeric(16)
        }

        Map dataBilling = [:]
        Map dataShipping = [:]

        // Metadata
        List customerMetadata = []
        customerMetadata.push(['key': '_wvs_customer_type', 'value': customer.type.name()])
        customerMetadata.push(['key': '_wvs_customer_active', 'value': customer.active ? '1' : '0'])
        customerMetadata.push(['key': '_wvs_customer_role', 'value': customer.role?.name() ?: ''])

        Contact mainContact = customer.contacts.find { it.type == ContactType.MAIN_CONTACT }
        Address mainAddress = customer.addresses.find { it.type == AddressType.MAIN_ADDRESS }
        Contact invoiceContact, deliveryContact
        Address invoiceAddress, deliveryAddress
        switch (customer.type) {
            case CustomerType.CUSTOMER:
                data.username = customer.number
                dataBilling.company = customer.name
                dataShipping.company = customer.name
                customerMetadata.push(['key': '_wvs_customer_number', 'value': customer.number])
                customerMetadata.push(['key': '_wvs_customer_name', 'value': customer.name])
                customerMetadata.push(['key': '_wvs_customer_currency', 'value': customer.currency?.name()])
                if (customer.priceClass) {
                    customerMetadata.push(['key': '_wvs_customer_price_class', 'value': customer.priceClass.vismaId])
                }
                if (packetService.isActivePacketOption(PacketOption.LOCATIONS) && customer.locations) {
                    customerMetadata.push(['key': '_wvs_customer_locations', 'value': mapCustomerLocations(customer.name, customer.locations)])
                }
                invoiceContact = customer.contacts.find { it.type == ContactType.INVOICE_CONTACT }
                invoiceAddress = customer.addresses.find { it.type == AddressType.INVOICE_ADDRESS }
                deliveryContact = customer.contacts.find { it.type == ContactType.DELIVERY_CONTACT }
                deliveryAddress = customer.addresses.find { it.type == AddressType.DELIVERY_ADDRESS }
                break
            case CustomerType.CONTACT:
                data.username = "${customer.customer.number}-${customer.vismaId}"
                dataBilling.company = customer.customer.name
                dataShipping.company = customer.customer.name
                customerMetadata.push(['key': '_wvs_customer_number', 'value': customer.customer?.number])
                customerMetadata.push(['key': '_wvs_customer_name', 'value': customer.customer?.name])
                customerMetadata.push(['key': '_wvs_customer_currency', 'value': customer.customer?.currency?.name()])
                if (customer.customer?.priceClass) {
                    customerMetadata.push(['key': '_wvs_customer_price_class', 'value': customer.customer.priceClass.vismaId])
                }
                if (packetService.isActivePacketOption(PacketOption.LOCATIONS) && customer.customer?.locations) {
                    customerMetadata.push(['key': '_wvs_customer_locations', 'value': mapCustomerLocations(customer.customer.name, customer.customer.locations)])
                }
                invoiceContact = customer.customer.contacts.find { it.type == ContactType.INVOICE_CONTACT }
                invoiceAddress = customer.customer.addresses.find { it.type == AddressType.INVOICE_ADDRESS }
                deliveryContact = customer.customer.contacts.find { it.type == ContactType.DELIVERY_CONTACT }
                deliveryAddress = customer.customer.addresses.find { it.type == AddressType.DELIVERY_ADDRESS }
                break
        }

        // Main contact
        if (mainContact) {
            data.first_name = mainContact?.firstName
            data.last_name = mainContact?.lastName
            data.email = mainContact?.email
        }

        // Invoice contact
        if (invoiceContact) {
            dataBilling.first_name = invoiceContact.firstName ?: ''
            dataBilling.last_name = invoiceContact.lastName ?: ''
            dataBilling.email = invoiceContact.email ?: ''
            dataBilling.phone = invoiceContact.phone1 ?: invoiceContact.phone2 ?: ''
        }

        // Invoice address
        if (invoiceAddress) {
            dataBilling.address_1 = invoiceAddress.addressLine1 ?: ''
            dataBilling.address_2 = invoiceAddress.addressLine2 ?: ''
            dataBilling.postcode = invoiceAddress.postalCode ?: ''
            dataBilling.city = invoiceAddress.city ?: ''
            dataBilling.state = invoiceAddress.county ?: ''
            dataBilling.country = invoiceAddress.countryCode ?: getWooCountryCode(invoiceAddress.country) ?: ''
        }

        // Delivery contact
        if (deliveryContact) {
            dataShipping.first_name = deliveryContact.firstName ?: ''
            dataShipping.last_name = deliveryContact.lastName ?: ''
            customerMetadata.push(['key': '_wvs_customer_shipping_email', 'value': deliveryContact.email ?: ''])
        }

        //Delivery address
        if (deliveryAddress) {
            dataShipping.address_1 = deliveryAddress.addressLine1 ?: ''
            dataShipping.address_2 = deliveryAddress.addressLine2 ?: ''
            dataShipping.postcode = deliveryAddress.postalCode ?: ''
            dataShipping.city = deliveryAddress.city ?: ''
            dataShipping.state = deliveryAddress.county ?: ''
            dataShipping.country = deliveryAddress.countryCode ?: getWooCountryCode(deliveryAddress.country) ?: ''
        }

        data.billing = dataBilling
        data.shipping = dataShipping
        data.meta_data = customerMetadata
        data
    }

    /**
     * Map customer locations
     * @param customerType as CustomerType
     * @param customerName as String
     * @param locations as Set of Location
     * @since 1.0.0_24 Only active locations will be visible in Woo (COBS-215)
     * @return
     */
    List mapCustomerLocations(String customerName, Set<Location> locations) {
        Map customerLocation = [:]
        List customerLocations = []
        locations.findAll { it.active }?.each { location ->
            location.refresh()
            customerLocation.name = location.name
            customerLocation.visma_id = location.vismaId
            customerLocation.company = customerName
            customerLocation.first_name = location.contact?.firstName ?: ''
            customerLocation.last_name = location.contact?.lastName ?: ''
            customerLocation.email = location.contact?.email ?: ''
            customerLocation.phone = location.contact?.phone1 ?: location.contact?.phone2 ?: ''
            customerLocation.address_1 = location.address?.addressLine1 ?: ''
            customerLocation.address_2 = location.address?.addressLine2 ?: ''
            customerLocation.postcode = location.address?.postalCode ?: ''
            customerLocation.city = location.address?.city ?: ''
            customerLocation.state = location.address?.county ?: ''
            customerLocation.country = location.address?.countryCode ?: getWooCountryCode(location.address?.country) ?: ''
            location.vismaId.toLowerCase() == 'main' || location.vismaId.toLowerCase() == 'primär' ?
                    customerLocations.add(0, customerLocation) :
                    customerLocations.push(customerLocation)
            customerLocation = [:]
        }
        customerLocations.push(['name':'Annan plats', 'visma_id':'other'])
        customerLocations
    }

    /**
     * Mapping a product to be suitable for import via the WooCommerce API
     * @param product as Product
     * @param toUpdate as Boolean(optional)
     * @param lang as Language(optional) - for WPML translation support
     * @return data as Map(suitable for WooCommerce API)
     */
    Map mapProductToWooApi(Product product, Boolean toUpdate = false, Language lang = null) {
        product.refresh()
        Map data = [:]
        data.meta_data = []

        if (toUpdate) {
            if (lang) {
                // For translations, check if ProductText has wooId
                ProductText productText = product.texts.find { it.lang == lang }
                if (!productText?.wooId) { return [:] }
                data.id = productText.wooId
            } else {
                // For main product
                if (!product.wooId) { return [:] }
                data.id = product.wooId
            }

            /*****************/
            /*** IMPORTANT ***/
            /*****************/
            /*
             * @link https://woocommerce.github.io/woocommerce-rest-api-docs/?shell#update-a-product
             */
            // Workaround for CWT-276
            // The "save_post" hook doesn't work while "put" a product without "date_created"
            data.date_created = product.dateCreated.format("yyyy-MM-dd'T'HH:mm:ss")
        }

        // Handle language-specific data for WPML
        if (lang) {
            ProductText productText = product.texts.find { it.lang == lang }

            // Use translated content if available, fallback to main product data
            data.name = productText?.description ?: product.description
            data.description = productText?.htmlDescription ?
                productText.htmlDescription.replaceAll(/<style>([\s\S]*)<\/style>/, '') :
                (product.htmlDescription ? product.cleanHtmlDescription : '')

            // WPML specific fields
            data.language = lang.code

            if (product.wooId) {
                data.translation_of = product.wooId
            }

            if (productText?.wooId) {
                data.id = productText.wooId
            }
        } else {
            // Default language behavior (main product)
            data.name = product.description
            data.description = product.htmlDescription ? product.cleanHtmlDescription : ''

            if (product.wooId) {
                data.id = product.wooId
            }
        }

        // SKU should be the same for all translations
        data.sku = product.number ?: ''

        if (product.type == ProductType.NON_STOCK_ITEM) {
            data.meta_data << ['key': '_wvs_is_non_stock_item', 'value': '1']
        }

        data.in_stock = true
        data.manage_stock = true
        data.backorders = 'yes'

        data.stock_quantity = product.availability

        // Service attributes
        product.attributes?.each {Attribute attribute ->
            switch (attribute.vismaId) {
                case webArticleAttributeVismaId:
                    data.meta_data << ['key': webArticleAttributeMetaDataKey, 'value': product.attributeTerms.find {
                        it.attribute.vismaId == webArticleAttributeVismaId
                    }?.name ?: '']
                    break
                case shortDescriptionAttributeVismaId:
                    data.short_description = product.attributeTerms.findAll {
                        it.attribute.vismaId == shortDescriptionAttributeVismaId
                    }?.collect { it.name }?.join('\r\n') ?: ''
                    break
                case hidePubliclyAttributeVismaId:
                    data.meta_data << ['key': hidePubliclyAttributeMetaDataKey, 'value': product.attributeTerms.find {
                        it.attribute.vismaId == hidePubliclyAttributeVismaId
                    }?.name ?: '']
                    break
                case licenceSoftwareAttributeVismaId:
                    data.meta_data << ['key': licenceSoftwareAttributeMetaDataKey, 'value': product.attributeTerms.find {
                        it.attribute.vismaId == licenceSoftwareAttributeVismaId
                    }?.name ?: '']
                    break
                case newProductAttributeVismaId:
                    data.meta_data << ['key': newProductAttributeMetaDataKey, 'value': product.attributeTerms.find {
                        it.attribute.vismaId == newProductAttributeVismaId
                    }?.getExpirationDateForNewProduct() ?: '']
                    break
            }
        }

        // Attributes/AttributeTerms
        if (product.attributeTerms) {
            List apiAttributes = []
            Map apiAttribute = [:]
            product.attributeTerms.findAll {AttributeTerm attributeTerm ->
                attributeTerm.attribute.wooId != null && attributeTerm.attribute.toSync
            }?.each { AttributeTerm attrTerm ->
                apiAttribute.id = attrTerm.attribute.wooId
                apiAttribute.options = [attrTerm.name]
                apiAttribute.visible = true
                apiAttribute.position = attrTerm.attribute.position ?: 0
                if (attrTerm.attribute.hidden) {
                    apiAttribute.visible = false
                }
                apiAttributes.push(apiAttribute)
                apiAttribute = [:]
            }
            data.attributes = apiAttributes
        } else {
            data.attributes = []
        }

        // Tags
        if (product.tags) {
            List apiTags = []
            Map apiTag = [:]
            product.tags.each { Tag tag ->
                apiTag.id = tag.wooId ?: tagService.sendTagToWoo(tag)
                apiTags.push(apiTag)
                apiTag = [:]
            }
            data.tags = apiTags
        } else {
            data.tags = []
        }

        // Categories
        if (product.categories) {
            List apiCategories = []
            Map apiCategory = [:]
            product.categories.each { Category cat ->
                if (cat.wooId) {
                    apiCategory.id = cat.wooId
                    apiCategories.push(apiCategory)
                    apiCategory = [:]
                }
            }
            switch (product.status) {
                case ProductStatus.INACTIVE:
                case ProductStatus.MARKED_FOR_DELETION:
                    data.categories = defaultCategoryId.isNumber() ? [['id': defaultCategoryId as Integer]] : []
                    break
                default:
                    data.categories = apiCategories
            }
        } else {
            data.categories = defaultCategoryId.isNumber() ? [['id': defaultCategoryId as Integer]] : []
        }

        // Images
        Set<Attachment> images = product.attachments.findAll {
            it.extension == FileExtension.JPG || it.extension == FileExtension.PNG
        }
        if (images) {
            List apiImages = []
            Map apiImage = [:]
            images.sort { it."$imageSortingField" }.eachWithIndex { Attachment img, Integer idx ->
                if (img.wooId) {
                    apiImage.id = img.wooId
                    apiImage.position = idx
                } else {
                    apiImage.name = img.name
                    apiImage.src = "${attachmentPath}/${img.name}"
                    apiImage.position = idx
                }
                apiImages.push(apiImage)
                apiImage = [:]
            }
            data.images = apiImages
        } else {
            data.images = []
        }

        // Files (pdf,doc)
        if (packetService.isActivePacketOption(PacketOption.FILES)) {
            Set<Attachment> files = product.attachments.findAll {
                it.extension == FileExtension.PDF || it.extension == FileExtension.DOC
            }
            if (files) {
                List apiFiles = []
                files.each {Attachment file ->
                    if (file.extension == FileExtension.PDF) {
                        String htmlSnippet = """
<div class="wvs description_file-link">
    <img src="${pdfIconUrn}" />
    <a href="${attachmentPath}/${file.name}" target="_blank">${file.originName}</a>
</div>
"""
                        apiFiles.push(htmlSnippet)
                    } else if (file.extension == FileExtension.DOC) {
                        String htmlSnippet = """
<div class="wvs description_file-link">
    <img src="${docIconUrn}" />
    <a href="${attachmentPath}/${file.name}" target="_blank">${file.originName}</a>
</div>
"""
                        apiFiles.push(htmlSnippet)
                    }
                }
//            data.short_description = apiFiles.join()
                data.meta_data << ['key': '_specifications', 'value': apiFiles.join()]
            } else {
                data.meta_data << ['key': '_specifications', 'value': '']
            }
        }

        // Prices
//        Price basePrice = product.prices.find { it.type == PriceType.BASE && !it.promotion && it.currency == Currency.SEK }
//        Price basePromoPrice = product.prices.find { it.type == PriceType.BASE && it.promotion && it.currency == Currency.SEK }
        Set<Price> basePrices = product.prices.findAll { it.type == PriceType.BASE && !it.promotion }
        Set<Price> basePromoPrices = product.prices.findAll { it.type == PriceType.BASE && it.promotion }
        Set<Price> customerPrices = product.prices.findAll { it.type == PriceType.CUSTOMER && !it.promotion }
        Set<Price> customerPromoPrices = product.prices.findAll { it.type == PriceType.CUSTOMER && it.promotion }
        Set<Price> customerPriceClassPrices = product.prices.findAll { it.type == PriceType.CUSTOMER_PRICE_CLASS && !it.promotion }
        Set<Price> customerPriceClassPromoPrices = product.prices.findAll { it.type == PriceType.CUSTOMER_PRICE_CLASS && it.promotion }
        if (basePromoPrices || customerPromoPrices || customerPriceClassPromoPrices) {
            data.meta_data << ['key': '_wvs_promo_price', 'value': '1']
        } else {
            data.meta_data << ['key': '_wvs_promo_price', 'value': '0']
        }
        data.price = ''
        data.regular_price = ''
        data.sale_price = ''
        Map priceData = [:]
        Map price = [:]
        Map discountPrice = [:]
        Map discountValue = [:]

        // Base promo price
        if (basePromoPrices) {
            basePromoPrices.each { Price basePromoPrice ->
                price."$basePromoPrice.currency" = basePromoPrice.price
            }
            data.meta_data << ['key': '_wvs_base_prices_promo', 'value': price]
        } else {
            data.meta_data << ['key': '_wvs_base_prices_promo', 'value': [:]]
        }

        // Base price
        if (basePrices) {
            price = [:]
            basePrices.each { Price basePrice ->
                price."$basePrice.currency" = basePrice.price
            }
            data.meta_data << ['key': '_wvs_base_prices', 'value': price]
        } else {
            data.meta_data << ['key': '_wvs_base_prices', 'value': [:]]
        }

        // Customer promo price
        if (customerPromoPrices) {
            price = [:]
            customerPromoPrices.each { Price customerPromoPrice ->
                if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
                    if (customerPromoPrice.customer.type == CustomerType.CUSTOMER) {
                        customerPromoPrice.customer.customerContacts.each { Customer customerContact ->
                            if (customerContact.wooId) {
                                price."${customerPromoPrice.currency}-${customerContact.wooId}" = customerPromoPrice.price
                            }
                        }
                    } else if (customerPromoPrice.customer.type == CustomerType.CONTACT && customerPromoPrice.customer.wooId) {
                        price."${customerPromoPrice.currency}-${customerPromoPrice.customer.wooId}" = customerPromoPrice.price
                    }
                } else {
                    if (customerPromoPrice.customer.wooId) {
                        price."${customerPromoPrice.currency}-${customerPromoPrice.customer.wooId}" = customerPromoPrice.price
                    }
                }
            }
            data.meta_data << ['key': '_wvs_customer_prices_promo', 'value': price]
        } else if (product.discounts && product.discounts.any{it.active} && basePrices) {
            discountPrice = [:]
            discountValue = [:]
            product.discounts.findAll{it.active}.each { Discount discount ->
                discount.customers.each { Customer customer ->
                    if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
                        switch (customer.type) {
                            case CustomerType.CUSTOMER:
                                customer.customerContacts.each { Customer customerContact ->
                                    if (customerContact.wooId) {
                                        discountPrice."${customer.currency}-${customerContact.wooId}" = priceService.getCustomerDiscountPrice(
                                                discount,
                                                basePrices.find {it.currency == customer.currency}?.price
                                        )
                                        discountValue."${customer.currency}-${customerContact.wooId}" = discount.readableDiscountValue
                                    }
                                }
                                break
                            case CustomerType.CONTACT:
                                if (customer.wooId) {
                                    discountPrice."${customer.customer.currency}-${customer.wooId}" = priceService.getCustomerDiscountPrice(
                                            discount,
                                            basePrices.find {it.currency.toString() == customer.customer.currency.toString()}?.price
                                    )
                                    discountValue."${customer.customer.currency}-${customer.wooId}" = discount.readableDiscountValue

                                }
                                break
                        }
                    } else {
                        if (customer.wooId) {
                            discountPrice."${customer.currency}-${customer.wooId}" = priceService.getCustomerDiscountPrice(
                                    discount,
                                    basePrices.find {it.currency.toString() == customer.currency.toString()}?.price
                            )
                            discountValue."${customer.currency}-${customer.wooId}" = discount.readableDiscountValue
                        }
                    }
                }
            }
            if (discountPrice) {
                data.meta_data << ['key': '_wvs_customer_prices_promo', 'value': discountPrice]
                data.meta_data << ['key': '_wvs_product_discounts', 'value': discountValue]
            } else {
                data.meta_data << ['key': '_wvs_customer_prices_promo', 'value': [:]]
                data.meta_data << ['key': '_wvs_product_discounts', 'value': [:]]
            }
        } else {
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_prices_promo')
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_product_discounts')
        }

        // Customer price
        if (customerPrices) {
            price = [:]
            discountPrice = [:]
            discountValue = [:]
            customerPrices.each { Price customerPrice ->
                if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
                    switch (customerPrice.customer.type) {
                        case CustomerType.CUSTOMER:
                            customerPrice.customer.customerContacts.each { Customer customerContact ->
                                if (customerContact.wooId && customerPrice.customer.discounts.any {it.products.contains(product)}) {
                                    discountPrice."${customerPrice.currency}-${customerContact.wooId}" = priceService.getCustomerDiscountPrice(customerPrice.customer, product, customerPrice.price)
                                    discountValue."${customerPrice.currency}-${customerContact.wooId}" = priceService.getCustomerDiscount(customerPrice.customer, product)?.readableDiscountValue
                                } else if (customerContact.wooId) {
                                    price."${customerPrice.currency}-${customerContact.wooId}" = customerPrice.price
                                }
                            }
                            break
                        case CustomerType.CONTACT:
                            if (customerPrice.customer.wooId && customerPrice.customer.customer.discounts.any {it.products.contains(product)}) {
                                discountPrice."${customerPrice.currency}-${customerPrice.customer.wooId}" = priceService.getCustomerDiscountPrice(customerPrice.customer.customer, product, customerPrice.price)
                                discountValue."${customerPrice.currency}-${customerPrice.customer.wooId}" = priceService.getCustomerDiscount(customerPrice.customer.customer, product)?.readableDiscountValue
                            } else if (customerPrice.customer.wooId) {
                                price."${customerPrice.currency}-${customerPrice.customer.wooId}" = customerPrice.price
                            }
                            break
                    }
                } else {
                    if (customerPrice.customer.wooId && customerPrice.customer.discounts.any {it.products.contains(product)}) {
                        discountPrice."${customerPrice.currency}-${customerPrice.customer.wooId}" = priceService.getCustomerDiscountPrice(customerPrice.customer.customer, product, customerPrice.price)
                        discountValue."${customerPrice.currency}-${customerPrice.customer.wooId}" = priceService.getCustomerDiscount(customerPrice.customer.customer, product)?.readableDiscountValue
                    } else if (customerPrice.customer.wooId) {
                        price."${customerPrice.currency}-${customerPrice.customer.wooId}" = customerPrice.price
                    }
                }
            }
            if (price) {
                data.meta_data << ['key': '_wvs_customer_prices', 'value': price]
            } else {
                data.meta_data << ['key': '_wvs_customer_prices', 'value': [:]]
            }

            if (discountPrice) {
                data.meta_data << ['key': '_wvs_customer_prices_promo', 'value': discountPrice]
                data.meta_data << ['key': '_wvs_customer_discounts', 'value': discountValue]
            } else {
                data.meta_data << ['key': '_wvs_customer_prices_promo', 'value': [:]]
                data.meta_data << ['key': '_wvs_customer_discounts', 'value': [:]]
            }
        } else {
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_prices')
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_prices_promo')
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_discounts')
        }

        // Price class promo price
        if (customerPriceClassPromoPrices) {
            price = [:]
            customerPriceClassPromoPrices.each { Price customerPriceClassPromoPrice ->
                if(customerPriceClassPromoPrice.priceClass) {
                    price."${customerPriceClassPromoPrice.currency}-${customerPriceClassPromoPrice.priceClass.vismaId}" = customerPriceClassPromoPrice.price
                }
            }
            data.meta_data << ['key': '_wvs_customer_class_prices_promo', 'value': price ?: [:]]
        } else if (product.priceClass && product.priceClass.discounts && product.priceClass.discounts.any{it.active} && basePrices) {
            discountPrice = [:]
            discountValue = [:]
            product.priceClass.discounts.findAll{it.active}.each { Discount discount ->
                discount.priceClasses.findAll {it.type == PriceClassType.CUSTOMER}?.each { PriceClass priceClass ->
                    discountPrice."SEK-${priceClass.vismaId}" = priceService.getCustomerDiscountPrice(
                            discount,
                            basePrices.find {it.currency == Currency.SEK}?.price
                    )
                    discountPrice."EUR-${priceClass.vismaId}" = priceService.getCustomerDiscountPrice(
                            discount,
                            basePrices.find {it.currency == Currency.EUR}?.price
                    )
                    discountValue."SEK-${priceClass.vismaId}" = discount.readableDiscountValue
                    discountValue."EUR-${priceClass.vismaId}" = discount.readableDiscountValue
                }
            }
            if (discountPrice) {
                data.meta_data << ['key': '_wvs_customer_class_prices_promo', 'value': discountPrice]
                data.meta_data << ['key': '_wvs_product_class_discounts', 'value': discountValue]
            } else {
                data.meta_data << ['key': '_wvs_customer_class_prices_promo', 'value': [:]]
                data.meta_data << ['key': '_wvs_product_class_discounts', 'value': [:]]
            }
        } else {
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_class_prices_promo')
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_product_class_discounts')
        }

        // Price class price
        if (customerPriceClassPrices) {
            price = [:]
            discountPrice = [:]
            discountValue = [:]
            customerPriceClassPrices.each { Price customerPriceClassPrice ->
                if(customerPriceClassPrice.priceClass && customerPriceClassPrice.priceClass.discounts && customerPriceClassPrice.priceClass.discounts.any{it.active}) {
                    discountPrice."${customerPriceClassPrice.currency}-${customerPriceClassPrice.priceClass.vismaId}" = priceService.getPriceClassDiscountPrice(product.priceClass, customerPriceClassPrice)
                    discountValue."${customerPriceClassPrice.currency}-${customerPriceClassPrice.priceClass.vismaId}" = priceService.getPriceClassDiscount(product.priceClass, customerPriceClassPrice)
                } else if (customerPriceClassPrice.priceClass) {
                    price."${customerPriceClassPrice.currency}-${customerPriceClassPrice.priceClass.vismaId}" = customerPriceClassPrice.price
                }
            }
            if (price) {
                data.meta_data << ['key': '_wvs_customer_class_prices', 'value': price]
            } else {
                data.meta_data << ['key': '_wvs_customer_class_prices', 'value': [:]]
            }

            if (discountPrice) {
                data.meta_data << ['key': '_wvs_customer_class_prices_promo', 'value': discountPrice]
                data.meta_data << ['key': '_wvs_customer_class_discounts', 'value': discountValue]
            } else {
                data.meta_data << ['key': '_wvs_customer_class_prices_promo', 'value': [:]]
                data.meta_data << ['key': '_wvs_customer_class_discounts', 'value': [:]]
            }
        } else {
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_class_prices')
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_class_prices_promo')
            data.meta_data = clearMetadata(data.meta_data as List, '_wvs_customer_class_discounts')
        }

        // Make specifications visible (compatible only with 'Electro' theme for WP)
        data.meta_data << ['key': '_specifications_display_attributes', 'value': 'yes']
//        data.meta_data << ['key': '_wvs_is_visible', 'value': productService.isVisible(product) ? '1' : '0']

        // Labels
        data.meta_data << ['key': '_wvs_product_label', 'value': productService.getProductLabels(product)]

        // Product class
        data.meta_data << ['key': '_wvs_product_class', 'value': product.productClass ?: '']
        data.meta_data << ['key': '_wvs_product_class_id', 'value': product.itemClassId ?: '']

        return data
    }

    private List clearMetadata(List metadata, String metaKey) {
        if (metadata.every { it instanceof Map } && !metadata.collect { it.key }.contains(metaKey)) {
            metadata << ['key': metaKey, 'value': [:]]
        }
        metadata
    }

    /**
     * Sending a category/subcategory to WooCommerce
     * @param data: Map
     * @param wooId: Integer
     * @return
     */
    Map<String, ?> sendCategoryToWoo(Map<String, ?> data, Integer wooId = null) {
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    wooId ? HttpMethod.PUT : HttpMethod.POST,
                    wooId ? "products/categories/$wooId" : "products/categories/",
                    getRequestConfig(
                            wooId ? HttpMethod.PUT : HttpMethod.POST,
                            wooId ? "products/categories/$wooId" : "products/categories/"
                    ),
                    data
            ) as Map<String, ?>
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Sending category to Woo', e)
            return null
        }
    }

    Boolean deleteCategoryFromWoo(Integer wooId) {
        if (!wooId) { return false }
        Map<String, ?> params = [
                'force':'true' // required to be true, as resource does not support trashing
        ]
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.DELETE,
                    "products/categories/$wooId",
                    getRequestConfig(HttpMethod.DELETE, "products/categories/$wooId"),
                    params
            )
        } catch (HttpException e) {
            if (e.message == 'Not Found') {
                return true
            }
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Deleting category in Woo', e)
            return false
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Deleting category in Woo', e)
            return false
        }
    }

    /**
     * Sending an Attribute to WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @return wooAttributeObj as Object
     */
    def sendAttributeToWoo(def data) {
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.POST,
                    'products/attributes',
                    getRequestConfig(HttpMethod.POST, 'products/attributes'),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Sending attribute to Woo', e)
            return null
        }
    }

    /**
     * Updating an attribute in WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @param attributeWooId as Long
     * @return wooAttributeObj as Object
     */
    def updateAttributeInWoo(def data, Long attributeWooId) {
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.PUT,
                    "products/attributes/${attributeWooId}",
                    getRequestConfig(HttpMethod.PUT, "products/attributes/${attributeWooId}"),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Updating attribute in Woo', e)
            return null
        }
    }

    /**
     * Sending an AttributeTerm to WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @return
     */
    def sendAttributeTermToWoo(def data, Integer wooAttributeId) {
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.POST,
                    "products/attributes/${wooAttributeId}/terms",
                    getRequestConfig(HttpMethod.POST, "products/attributes/${wooAttributeId}/terms"),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Sending attribute term to Woo', e)
            return null
        }
    }

    /**
     * Sending a customer to WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @return wooCustomerObj as Object
     */
    def sendCustomerToWoo(def data) {
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.POST,
                    'customers',
                    getRequestConfig(HttpMethod.POST, 'customers'),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Sending customer to Woo', e)
            return null
        }
    }

    /**
     * Updating a customer in WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @param customerWooId as Long
     * @return wooCustomerObj as Object
     */
    def updateCustomerInWoo(def data, Long customerWooId) {
        try {
            httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.PUT,
                    "customers/${customerWooId}",
                    getRequestConfig(HttpMethod.PUT, "customers/${customerWooId}"),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Updating customer in Woo', e)
            return null
        }
    }

    /**
     * Sending a product to WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @return
     */
    def sendProductToWoo(def data) throws Exception {
        httpService.doApiCall(
                ApiService.WOO,
                HttpMethod.POST,
                'products',
                getRequestConfig(HttpMethod.POST, 'products'),
                data
        )
    }

    /**
     * Updating a product in WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @param productWooId as Long
     * @return
     */
    def updateProductInWoo(def data, Long productWooId) throws Exception {
        httpService.doApiCall(
                ApiService.WOO,
                HttpMethod.PUT,
                "products/${productWooId}",
                getRequestConfig(HttpMethod.PUT, "products/${productWooId}"),
                data
        )
    }

    /**
     * Getting all product from WooCommerce
     * !IMPORTANT No more than 100 products per page according to WooCommerce requirements
     * @param products as List(optional)
     * @param params as Map(optional)
     * @return wooProducts as List<Object>
     */
    def getProducts(List<Object> products = [], Map<String, ?> params = [page: 1, per_page: 100]) {
        Integer resultsPerPage = params.per_page as Integer
        try {
            List<Object> batch = httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.GET,
                    'products',
                    getRequestConfig(HttpMethod.GET, 'products'),
                    params) as List
            if (!batch) {
                return products
            } else if (batch.size() == resultsPerPage) {
                products.push(batch)
                params.page++
                params.per_page = resultsPerPage
                getProducts(products, params)
            } else {
                products.push(batch)
                return products.flatten()
            }
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Getting all products from Woo', e)
            return null
        }
    }

    /**
     * Batch update and delete multiple products
     * @param productsToCreate as List of Product
     * @param productsToUpdate as List of Product
     * @param productsToUpdate as List of wooIds
     * @retur result as Object
     */
    def batchUpdateProducts(List<Product> productsToCreate, List<Product> productsToUpdate, List<Long> productsToDelete) {
        Map data = [:]
        if (productsToCreate) {
            List toCreate = []
            productsToCreate.each { Product productToCreate ->
                toCreate << mapProductToWooApi(productToCreate)
            }
            data.create = toCreate
        }
        if (productsToUpdate) {
            List toUpdate = []
            productsToUpdate.each { Product productToUpdate ->
                toUpdate << mapProductToWooApi(productToUpdate, true)
            }
            data.update = toUpdate
        }
        if (productsToDelete) {
            data.delete = productsToDelete
        }

        if (data) {
            try {
                return httpService.doApiCall(
                        ApiService.WOO,
                        HttpMethod.POST,
                        'products/batch',
                        getRequestConfig(HttpMethod.POST, 'products/batch'),
                        data
                )
            } catch (Exception e) {
                errorHandlerService.logErrorDetails('ERROR-WHILE', 'Updating a batch of products in Woo', e)
                return null
            }
        } else {
            return null
        }
    }

    /**
     * Mapping a tag to be suitable for import via the WooCommerce API
     * @param tag as Tag
     * @return data as Map(suitable for WooCommerce API)
     */
    Map mapTagToWooApi(Tag tag, Boolean toUpdate = false) {
        Map data = [:]

        if (toUpdate) {
            if (!tag.wooId) { return null }
            data.id = tag.wooId
        }

        data.name = tag.name.isNumber() ? "v${tag.name}" : tag.name
        if (tag.description) { data.description = tag.description }
        if (tag.slug) {
            data.slug = tag.slug
        } else if (tag.name.isNumber()) {
            data.slug = "v${tag.name}" as String
        }

        return data
    }

    /**
     * Sending a Tag to WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @return wooTagObj as Object
     */
    def sendTagToWoo(def data) {
        try {
            return httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.POST,
                    'products/tags',
                    getRequestConfig(HttpMethod.POST, 'products/tags'),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Sending tag to Woo', e)
            return null
        }
    }

    /**
     * Updating the Tag in WooCommerce
     * @param data as Object(suitable for WooCommerce API)
     * @return wooTagObj as Object
     */
    def updateTagInWoo(def data) {
        try {
            return httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.PUT,
                    "products/tags/${data.id}",
                    getRequestConfig(HttpMethod.PUT, "products/tags/${data.id}"),
                    data
            )
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Updating tag in Woo', e)
            return null
        }
    }

    /**
     * Generic method for batch update purposes
     * @param batchType as BatchUpdateType
     * @param toCreate as List
     * @param toUpdate as List
     * @param toDelete as List
     * @return
     */
    def batchUpdate(BatchUpdateType batchType, List<Object> toCreate, List<Object> toUpdate, List<Long> toDelete) {
        Map data = [:]
        switch (batchType) {
            case BatchUpdateType.CUSTOMER:
                if (toCreate) {
                    List customersToCreate = []
                    toCreate.each {customerToCreate ->
                        if (customerToCreate instanceof Customer) {
                            customersToCreate << mapCustomerToWooApi(customerToCreate)
                        }
                    }
                    data.create = customersToCreate
                }
                if (toUpdate) {
                    List customersToUpdate = []
                    toUpdate.each { customerToUpdate ->
                        if (customerToUpdate instanceof Customer) {
                            customersToUpdate << mapCustomerToWooApi(customerToUpdate, true)
                        }
                    }
                    data.update = customersToUpdate
                }
                if (toDelete) {
                    data.delete = toDelete
                }
                break
            case BatchUpdateType.PRODUCT:
                if (toCreate) {
                    List productsToCreate = []
                    toCreate.each { productToCreate ->
                        if (productToCreate instanceof Product) {
                            productsToCreate << mapProductToWooApi(productToCreate)
                        }
                    }
                    data.create = productsToCreate
                }
                if (toUpdate) {
                    List productsToUpdate = []
                    toUpdate.each { productToUpdate ->
                        if (productToUpdate instanceof Product) {
                            productsToUpdate << mapProductToWooApi(productToUpdate, true)
                        }
                    }
                    data.update = productsToUpdate
                }
                if (toDelete) {
                    data.delete = toDelete
                }
                break
            case BatchUpdateType.TAG:
                if (toCreate) {
                    List tagsToCreate = []
                    toCreate.each { tagToCreate ->
                        if (tagToCreate instanceof Tag) {
                            tagsToCreate << mapTagToWooApi(tagToCreate)
                        }
                    }
                    data.create = tagsToCreate
                }
                if (toUpdate) {
                    List tagsToUpdate = []
                    toUpdate.each { tagToUpdate ->
                        if (tagToUpdate instanceof Tag) {
                            tagsToUpdate << mapTagToWooApi(tagToUpdate, true)
                        }
                    }
                    data.update = tagsToUpdate
                }
                if (toDelete) {
                    data.delete = toDelete
                }
                break
            default:
                return
        }

        if (data) {
            try {
                return httpService.doApiCall(
                        ApiService.WOO,
                        HttpMethod.POST,
                        "${batchType}",
                        getRequestConfig(HttpMethod.POST, "${batchType}"),
                        data
                )
            } catch (Exception e) {
                errorHandlerService.logErrorDetails('ERROR-WHILE', 'Updating a batch of entities in Woo', e)
                return null
            }
        } else {
            return null
        }
    }

    /**
     * Get all tag ids
     * !IMPORTANT No more than 100 tags per page according to WooCommerce requirements
     * @return
     */
    List<Long> getTagIdsInBatches() {
        Integer page = 1
        List<Long> tagIds = []
        List<Object> batch
        try {
            while (true) {
                batch = httpService.doApiCall(
                        ApiService.WOO,
                        HttpMethod.GET,
                        'products/tags',
                        getRequestConfig(HttpMethod.GET, 'products/tags'),
                        [page:page, per_page:100]
                ) as List
                if (!batch) {
                    break
                } else {
                    tagIds += batch?.collect { it.id }
                    page++
                }
            }
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Getting all tag IDs from Woo', e)
            return null
        }
        return tagIds
    }

    def findCustomerByEmail(String email) {
        if (email == null) {
            return null
        }
        Map<String, String> params = ['email': email]
        List<Object> wooCustomers = httpService.doApiCall(
                ApiService.WOO,
                HttpMethod.GET,
                'customers',
                getRequestConfig(HttpMethod.GET, 'customers', params),
                params
        ) as List
        if (wooCustomers && wooCustomers.size() == 1) {
            return wooCustomers[0]
        }
        return null
    }

    List<Object> getCategories(List<Object> categories = [], Map<String, ?> params = [page: 1, per_page: 100]) {
        try {
            List<Object> batchOfCategories = httpService.doApiCall(
                    ApiService.WOO,
                    HttpMethod.GET,
                    'products/categories',
                    getRequestConfig(HttpMethod.GET, 'products/categories'),
                    params) as List<Object>
            if (batchOfCategories.size() > 0) {
                categories.push(batchOfCategories)
                params.page++
                getCategories(categories, params)
            } else {
                categories.push(batchOfCategories)
                return categories?.flatten() ?: null
            }
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('ERROR-WHILE', 'Getting all categories from Woo', e)
            return null
        }
    }

}
